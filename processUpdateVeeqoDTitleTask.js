/**
 * Process update_veeqo_d_title task
 * 
 * This task updates a disc's Veeqo product title to match the disc's g_pull value.
 * It uses the disc's SKU (format: 'D' + disc.id) to find the Veeqo product
 * and updates the product title to the g_pull value.
 */

import fetch from 'node-fetch';
import getVeeqoId from './getVeeqoId.js';

const veeqoApiKey = process.env.VEEQO_API_KEY;

if (!veeqoApiKey) {
  console.error('❌ VEEQO_API_KEY environment variable is not set');
}

// Function to make Veeqo API request
async function makeVeeqoRequest(endpoint, method = 'GET', data = null) {
  try {
    const options = {
      method,
      headers: {
        'Content-Type': 'application/json',
        'x-api-key': veeqoApiKey
      }
    };
    
    if (data && (method === 'POST' || method === 'PUT' || method === 'PATCH')) {
      options.body = JSON.stringify(data);
    }
    
    const response = await fetch(endpoint, options);
    
    if (!response.ok) {
      const errorText = await response.text();
      return { success: false, error: `${response.status}: ${errorText}`, status: response.status };
    }
    
    const result = await response.json();
    return { success: true, data: result };
  } catch (error) {
    return { success: false, error: error.message };
  }
}

// Function to update Veeqo product title
async function updateVeeqoProductTitle(productId, newTitle) {
  console.log(`[processUpdateVeeqoDTitleTask] Updating Veeqo product ${productId} title to: "${newTitle}"`);
  
  const result = await makeVeeqoRequest(
    `https://api.veeqo.com/products/${productId}`,
    'PUT',
    { product: { title: newTitle } }
  );
  
  if (result.success) {
    console.log(`[processUpdateVeeqoDTitleTask] Successfully updated product title`);
    return result.data;
  } else {
    console.error(`[processUpdateVeeqoDTitleTask] Failed to update product title: ${result.error}`);
    throw new Error(`Failed to update Veeqo product title: ${result.error}`);
  }
}

// Main task processor function
export async function processUpdateVeeqoDTitleTask(task, { supabase, updateTaskStatus, logError }) {
  console.log(`[processUpdateVeeqoDTitleTask] Processing task ${task.id}`);
  
  try {
    const payload = task.payload;
    
    if (!payload || !payload.id) {
      throw new Error('Task payload missing required id field');
    }
    
    const discId = payload.id;
    const newTitle = payload.g_pull;
    const oldTitle = payload.old_g_pull;
    
    console.log(`[processUpdateVeeqoDTitleTask] Disc ID: ${discId}`);
    console.log(`[processUpdateVeeqoDTitleTask] New title: "${newTitle}"`);
    console.log(`[processUpdateVeeqoDTitleTask] Old title: "${oldTitle}"`);
    
    // Validate that we have a new title
    if (!newTitle || newTitle.trim() === '') {
      throw new Error('New title (g_pull) is empty or null');
    }
    
    // Update task to 'processing' status
    await updateTaskStatus(task.id, 'processing');
    
    // Get the disc's SKU
    const sku = `D${discId}`;
    console.log(`[processUpdateVeeqoDTitleTask] Looking for Veeqo product with SKU: ${sku}`);
    
    // Get the Veeqo product IDs from the SKU
    const veeqoIds = await getVeeqoId(sku);
    
    if (!veeqoIds || veeqoIds.length === 0) {
      console.log(`[processUpdateVeeqoDTitleTask] No Veeqo product found for SKU ${sku}`);
      await updateTaskStatus(task.id, 'completed', {
        message: `No Veeqo product found for SKU ${sku}. Disc may not be uploaded to Veeqo yet.`,
        disc_id: discId,
        sku: sku,
        new_title: newTitle
      });
      return;
    }
    
    console.log(`[processUpdateVeeqoDTitleTask] Found ${veeqoIds.length} Veeqo product(s) for SKU ${sku}`);
    
    // Update title for all Veeqo products with this SKU
    let successCount = 0;
    let failureCount = 0;
    let errors = [];
    
    for (const veeqoId of veeqoIds) {
      try {
        console.log(`[processUpdateVeeqoDTitleTask] Updating title for Veeqo product ID ${veeqoId}`);
        
        const updatedProduct = await updateVeeqoProductTitle(veeqoId, newTitle);
        
        console.log(`[processUpdateVeeqoDTitleTask] Successfully updated Veeqo product ${veeqoId}`);
        successCount++;
        
      } catch (error) {
        console.error(`[processUpdateVeeqoDTitleTask] Failed to update Veeqo product ${veeqoId}: ${error.message}`);
        failureCount++;
        errors.push(`Product ${veeqoId}: ${error.message}`);
      }
    }
    
    // Determine final task status
    if (successCount > 0 && failureCount === 0) {
      // All updates successful
      await updateTaskStatus(task.id, 'completed', {
        message: `Successfully updated title for ${successCount} Veeqo product(s)`,
        disc_id: discId,
        sku: sku,
        new_title: newTitle,
        old_title: oldTitle,
        veeqo_products_updated: successCount,
        veeqo_ids: veeqoIds
      });
    } else if (successCount > 0 && failureCount > 0) {
      // Partial success
      await updateTaskStatus(task.id, 'completed', {
        message: `Partially successful: ${successCount} updated, ${failureCount} failed`,
        disc_id: discId,
        sku: sku,
        new_title: newTitle,
        old_title: oldTitle,
        veeqo_products_updated: successCount,
        veeqo_products_failed: failureCount,
        errors: errors,
        veeqo_ids: veeqoIds
      });
    } else {
      // All updates failed
      throw new Error(`Failed to update any Veeqo products. Errors: ${errors.join('; ')}`);
    }
    
  } catch (error) {
    const errMsg = `[processUpdateVeeqoDTitleTask] Exception while processing task ${task.id}: ${error.message}`;
    console.error(errMsg);
    await logError(errMsg, `Processing task ${task.id}`);
    
    await updateTaskStatus(task.id, 'error', {
      message: "Failed to update Veeqo product title due to an unexpected error.",
      error: error.message,
      disc_id: task.payload?.id,
      new_title: task.payload?.g_pull
    });
  }
}

export default processUpdateVeeqoDTitleTask;
