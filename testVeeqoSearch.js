import fetch from 'node-fetch';
import dotenv from 'dotenv';

dotenv.config();

const veeqoApiKey = process.env.VEEQO_API_KEY;

async function testVeeqoSearch() {
  console.log('🔍 Testing Veeqo search for SKU D424515...');
  
  const sku = 'D424515';
  const url = `https://api.veeqo.com/products?query=${encodeURIComponent(sku)}`;
  
  console.log('📡 URL:', url);
  
  const response = await fetch(url, {
    method: 'GET',
    headers: {
      'Content-Type': 'application/json',
      'x-api-key': veeqoApiKey
    }
  });
  
  if (!response.ok) {
    console.error('❌ API Error:', response.status, response.statusText);
    return;
  }
  
  const data = await response.json();
  console.log('📋 Found', data.length, 'products');
  
  data.forEach((product, index) => {
    console.log(`${index + 1}. Product ${product.id}: "${product.title}"`);
    if (product.sellables) {
      product.sellables.forEach(sellable => {
        console.log(`   Sellable SKU: ${sellable.sku_code}`);
      });
    }
  });
  
  // Filter for exact SKU match
  const matchingProducts = data.filter(product => {
    if (product.sellables && Array.isArray(product.sellables)) {
      return product.sellables.some(sellable => sellable.sku_code === sku);
    }
    return false;
  });
  
  console.log('✅ Exact matches:', matchingProducts.length);
  
  if (matchingProducts.length > 0) {
    console.log('📋 Matching products:');
    matchingProducts.forEach(product => {
      console.log(`   Product ${product.id}: "${product.title}"`);
    });
  }
}

testVeeqoSearch();
