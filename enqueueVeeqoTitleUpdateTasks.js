/**
 * One-time script to enqueue update_veeqo_d_title tasks for existing discs
 * 
 * This script finds all discs that:
 * - Have sold_date = null (unsold)
 * - Have shopify_uploaded_at IS NOT NULL (uploaded to Shopify/Veeqo)
 * - Have g_pull IS NOT NULL (have a title to update to)
 * 
 * And enqueues update_veeqo_d_title tasks for them.
 */

import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

dotenv.config();

// Initialize Supabase client
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('❌ Missing required environment variables: SUPABASE_URL and/or SUPABASE_KEY');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

// Parse command line arguments
const args = process.argv.slice(2);
const dryRun = args.includes('--dry-run');
const batchSize = parseInt(args.find(arg => arg.startsWith('--batch-size='))?.split('=')[1]) || 100;
const delay = parseInt(args.find(arg => arg.startsWith('--delay='))?.split('=')[1]) || 1;
const startId = parseInt(args.find(arg => arg.startsWith('--start-id='))?.split('=')[1]) || 0;
const limit = parseInt(args.find(arg => arg.startsWith('--limit='))?.split('=')[1]) || null;

console.log('🔄 VEEQO TITLE UPDATE TASK ENQUEUER');
console.log('='.repeat(50));
console.log(`📋 Configuration:`);
console.log(`   Dry run: ${dryRun ? 'YES' : 'NO'}`);
console.log(`   Batch size: ${batchSize}`);
console.log(`   Delay between batches: ${delay} seconds`);
console.log(`   Start ID: ${startId}`);
console.log(`   Limit: ${limit || 'No limit'}`);
console.log('');

async function getEligibleDiscs() {
  console.log('🔍 Finding eligible discs...');
  
  let query = supabase
    .from('t_discs')
    .select('id, g_pull, shopify_uploaded_at')
    .is('sold_date', null)
    .not('shopify_uploaded_at', 'is', null)
    .not('g_pull', 'is', null)
    .gte('id', startId)
    .order('id', { ascending: true });
  
  if (limit) {
    query = query.limit(limit);
  }
  
  const { data, error } = await query;
  
  if (error) {
    console.error('❌ Error fetching eligible discs:', error);
    throw error;
  }
  
  console.log(`✅ Found ${data.length} eligible discs`);
  return data;
}

async function enqueueTasks(discs) {
  console.log(`\n📦 Preparing to enqueue ${discs.length} tasks...`);
  
  if (dryRun) {
    console.log('🧪 DRY RUN - No tasks will actually be enqueued');
    console.log('\nSample of discs that would be processed:');
    discs.slice(0, 5).forEach(disc => {
      console.log(`   Disc ${disc.id}: "${disc.g_pull}"`);
    });
    return;
  }
  
  const now = new Date();
  let tasksEnqueued = 0;
  let errors = 0;
  
  // Process in batches
  for (let i = 0; i < discs.length; i += batchSize) {
    const batch = discs.slice(i, i + batchSize);
    
    console.log(`\n📋 Processing batch ${Math.floor(i / batchSize) + 1} of ${Math.ceil(discs.length / batchSize)} (${batch.length} discs)`);
    
    // Prepare tasks for this batch
    const tasks = batch.map((disc, index) => {
      // Stagger the scheduled times slightly to avoid overwhelming the system
      const scheduledAt = new Date(now.getTime() + (5 * 60 * 1000) + (index * 1000)); // 5 minutes + index seconds
      
      return {
        task_type: 'update_veeqo_d_title',
        payload: {
          id: disc.id,
          g_pull: disc.g_pull,
          old_g_pull: null // No old value for initial sync
        },
        status: 'pending',
        scheduled_at: scheduledAt.toISOString(),
        created_at: now.toISOString(),
        enqueued_by: 'enqueueVeeqoTitleUpdateTasks.js_initial_sync'
      };
    });
    
    try {
      // Insert tasks in smaller chunks to avoid potential issues
      const CHUNK_SIZE = 50;
      for (let j = 0; j < tasks.length; j += CHUNK_SIZE) {
        const chunk = tasks.slice(j, j + CHUNK_SIZE);
        
        const { error: insertError } = await supabase
          .from('t_task_queue')
          .insert(chunk);
        
        if (insertError) {
          console.error(`❌ Error enqueueing tasks chunk ${Math.floor(j / CHUNK_SIZE) + 1}:`, insertError);
          errors += chunk.length;
        } else {
          tasksEnqueued += chunk.length;
          console.log(`   ✅ Enqueued chunk ${Math.floor(j / CHUNK_SIZE) + 1} (${chunk.length} tasks)`);
        }
      }
      
    } catch (error) {
      console.error(`❌ Error processing batch:`, error);
      errors += batch.length;
    }
    
    // Add delay between batches if specified
    if (delay > 0 && i + batchSize < discs.length) {
      console.log(`⏱️  Waiting ${delay} seconds before next batch...`);
      await new Promise(resolve => setTimeout(resolve, delay * 1000));
    }
  }
  
  console.log(`\n📊 Summary:`);
  console.log(`   ✅ Tasks enqueued: ${tasksEnqueued}`);
  console.log(`   ❌ Errors: ${errors}`);
  console.log(`   📋 Total processed: ${tasksEnqueued + errors}`);
}

async function main() {
  try {
    const discs = await getEligibleDiscs();
    
    if (discs.length === 0) {
      console.log('ℹ️  No eligible discs found. Nothing to do.');
      return;
    }
    
    await enqueueTasks(discs);
    
    console.log('\n🎉 Script completed successfully!');
    
  } catch (error) {
    console.error('❌ Script failed:', error);
    process.exit(1);
  }
}

// Show usage if --help is provided
if (args.includes('--help')) {
  console.log('Usage: node enqueueVeeqoTitleUpdateTasks.js [options]');
  console.log('');
  console.log('Options:');
  console.log('  --dry-run              Run without actually enqueueing tasks');
  console.log('  --batch-size=N         Number of tasks to process in each batch (default: 100)');
  console.log('  --delay=N              Seconds to wait between batches (default: 1)');
  console.log('  --start-id=N           Start processing from disc ID N (default: 0)');
  console.log('  --limit=N              Limit total number of discs to process');
  console.log('  --help                 Show this help message');
  console.log('');
  console.log('Examples:');
  console.log('  node enqueueVeeqoTitleUpdateTasks.js --dry-run');
  console.log('  node enqueueVeeqoTitleUpdateTasks.js --batch-size=50 --delay=2');
  console.log('  node enqueueVeeqoTitleUpdateTasks.js --start-id=10000 --limit=1000');
  process.exit(0);
}

// Run the script
main();
